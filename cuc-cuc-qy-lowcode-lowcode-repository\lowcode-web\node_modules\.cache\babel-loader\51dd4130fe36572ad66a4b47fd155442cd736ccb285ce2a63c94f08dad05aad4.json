{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = exports.constantRouterMap = void 0;\nexports.resetRouter = resetRouter;\nrequire(\"core-js/modules/es.array.push.js\");\nvar _interopRequireWildcard2 = _interopRequireDefault(require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\"));\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _vueRouter = _interopRequireDefault(require(\"vue-router\"));\nvar _BasicLayout = _interopRequireDefault(require(\"../layouts/BasicLayout.vue\"));\nvar _EmptyLayout = _interopRequireDefault(require(\"../layouts/EmptyLayout.vue\"));\nvar _dataModal = _interopRequireDefault(require(\"@admin/views/dataModal/dataModal.vue\"));\nvar _modalDetail = _interopRequireDefault(require(\"@admin/views/dataModal/modalDetail.vue\"));\nvar _dataBackend = _interopRequireDefault(require(\"@admin/views/dataModal/dataBackend/dataBackend.vue\"));\nvar _dataManage = _interopRequireDefault(require(\"@admin/views/dataModal/dataBackend/dataManage.vue\"));\nvar _add = _interopRequireDefault(require(\"@admin/views/dataModal/dataBackend/data/add.vue\"));\nvar _edit = _interopRequireDefault(require(\"@admin/views/dataModal/dataBackend/data/edit.vue\"));\nvar _detail = _interopRequireDefault(require(\"@admin/views/dataModal/dataBackend/data/detail.vue\"));\nvar _personal = _interopRequireDefault(require(\"../views/information/personal.vue\"));\nvar _list = _interopRequireDefault(require(\"../components/Template/list.vue\"));\nvar _statisticalAnalysis = _interopRequireDefault(require(\"@admin/views/information/statisticalAnalysis.vue\"));\nvar _actionExternal = _interopRequireDefault(require(\"@admin/views/fillData/actionExternal.vue\"));\nvar _templateManagement = _interopRequireDefault(require(\"@admin/views/information/templateManagement.vue\"));\nvar _bus = _interopRequireDefault(require(\"@/utils/bus\"));\n// import bigscreen from '@admin/views/bigscreen/index.vue'\n\n_vue.default.use(_vueRouter.default);\nvar constantRouterMap = exports.constantRouterMap = [{\n  path: '/login',\n  name: 'login',\n  meta: {\n    title: '登录',\n    noCache: true\n  },\n  component: resolve => require(['@admin/views/login'], resolve),\n  hidden: true\n}, {\n  path: '/404',\n  name: 'static-404',\n  component: resolve => require(['@admin/views/features/404'], resolve),\n  hidden: true\n}, {\n  path: '/401',\n  name: 'static-401',\n  component: resolve => require(['@admin/views/features/401'], resolve),\n  hidden: true\n}, {\n  path: '/403',\n  name: 'static-403',\n  component: resolve => require(['@admin/views/features/403'], resolve),\n  hidden: true\n}, {\n  path: '/redirect',\n  name: 'static-redirect',\n  component: _BasicLayout.default,\n  hidden: true,\n  children: [{\n    path: '/redirect/:path*',\n    component: resolve => require(['@admin/views/features/redirect'], resolve)\n  }]\n}, {\n  path: '/statisticalAnalysis',\n  name: 'statisticalAnalysis',\n  component: _statisticalAnalysis.default,\n  meta: {\n    title: '数据统计分析',\n    noCache: true\n  }\n}, {\n  path: '/fillActionExternal',\n  name: 'fillActionExternal',\n  component: _actionExternal.default,\n  meta: {\n    title: '外部填报',\n    noCache: true\n  }\n},\n// {\n//   path:'/actionExternalDetail',\n//   name: 'actionExternalDetail',\n//   component: resolve => require(['@admin/views/fillData/actionExternalDetail'], resolve),\n//   meta:{\n//     title: '外部填报详情', noCache: true,\n//   }\n// },\n// {\n// path: '/',\n// component: BasicLayout,\n// name: 'static-home',\n// redirect: '/dashboard/index',\n// },\n\n// {\n//   path: '/',\n//   component: Layout,\n//   redirect: '/dashboard',\n//   // hidden: true,\n//   children: [\n//     {\n//       path: 'dashboard',\n//       component: (resolve) => require(['@admin/views/home'], resolve),\n//       name: 'Dashboard',\n//       meta: { title: '首页', icon: 'home-page', affix: true, noCache: true }\n//     }\n//   ]\n// },\n\n{\n  path: '/user',\n  name: 'static-user',\n  component: _BasicLayout.default,\n  hidden: true,\n  redirect: 'noredirect',\n  children: [{\n    path: 'center',\n    component: resolve => require(['@admin/views/system/user/center'], resolve),\n    name: '个人中心',\n    meta: {\n      title: '个人中心'\n    }\n  }]\n}, {\n  path: '/tool/gen-edit',\n  component: _BasicLayout.default,\n  hidden: true,\n  permissions: ['tool:gen:edit'],\n  children: [{\n    path: 'index/:tableId(\\\\d+)',\n    component: () => Promise.resolve().then(() => (0, _interopRequireWildcard2.default)(require('@admin/views/tools/codeGen/editTable'))),\n    name: 'GenEdit',\n    meta: {\n      title: '修改生成配置',\n      activeMenu: '/tool/gen'\n    }\n  }]\n}, {\n  path: '/formManager/own/add',\n  name: 'ownFormAdd',\n  component: resolve => require(['@admin/views/formManager/own/add'], resolve),\n  hidden: true\n},\n// {\n//   path: '/formManager/formAllDetail',\n//   name: 'formManagerFormAllDetail',\n//   component: resolve => require(['@admin/views/formManager/formAllDetail'], resolve),\n//   hidden: true,\n// },\n{\n  path: '/formManager/own/edit',\n  name: 'ownFormEdit',\n  component: resolve => require(['@admin/views/formManager/own/edit'], resolve),\n  hidden: true\n}, {\n  path: '/processManager/add',\n  name: 'processAdd',\n  component: resolve => require(['@admin/views/processManager/add'], resolve),\n  hidden: true\n}, {\n  path: '/fillData/action/index',\n  name: 'fillAction',\n  component: resolve => require(['@admin/views/fillData/action'], resolve),\n  hidden: true\n}, {\n  path: '/processManager/edit',\n  name: 'processEdit',\n  component: resolve => require(['@admin/views/processManager/edit'], resolve),\n  hidden: true\n},\n// 本地路由\n// {\n//   path: '/dataModel',\n//   name: 'dataModel',\n//   component: DataModal,\n//   meta: {\n//     title: '数据模型',\n//   }\n// },\n// {\n//   path: '/fieldManage',\n//   name: 'fieldManage',\n//   component: resolve => require(['@admin/views/fieldManage/index'], resolve),\n//   hidden: true,\n// },\n// {\n//   path: '/dataModel',\n//   name: 'dataModel',\n//   component: DataModal,\n//   meta: {\n//     title: '数据模型',\n//   }\n// },\n{\n  path: '/basicSheet',\n  name: 'basicSheet',\n  component: _BasicLayout.default,\n  hidden: true,\n  redirect: 'noredirect',\n  children: [{\n    path: '/modelDetail',\n    name: 'modalDetail',\n    component: _modalDetail.default,\n    meta: {\n      title: '数据模型',\n      closeConfirm: '0'\n    }\n  }, {\n    path: '/dataBackend',\n    name: 'dataBackend',\n    component: _dataBackend.default,\n    meta: {\n      title: '数据管理后台',\n      closeConfirm: '0'\n    }\n  }, {\n    path: '/dataManage',\n    name: 'dataManage',\n    component: _dataManage.default,\n    meta: {\n      title: '数据管理后台',\n      closeConfirm: '0'\n    }\n  }, {\n    path: '/addData',\n    name: 'addData',\n    component: _add.default,\n    meta: {\n      title: '数据管理',\n      closeConfirm: '0'\n    }\n  }, {\n    path: '/editData',\n    name: 'editData',\n    component: _edit.default,\n    meta: {\n      title: '数据管理',\n      closeConfirm: '0'\n    }\n  }, {\n    path: '/viewData',\n    name: 'viewData',\n    component: _detail.default,\n    meta: {\n      title: '数据管理',\n      closeConfirm: '0'\n    }\n  }\n  // {\n  //   path: '/bigscreen',\n  //   name: 'bigscreen',\n  //   component: bigscreen,\n  //   meta: {\n  //     title: '大屏',\n  //     closeConfirm:'0'\n  //   },\n  // },\n  ]\n}];\nconst createRouter = () => new _vueRouter.default({\n  //mode: 'hash',\n  mode: 'history',\n  base: process.env.VUE_APP_PC_PATH + '/',\n  scrollBehavior: () => ({\n    y: 0\n  }),\n  routes: constantRouterMap\n});\nconst router = createRouter();\nfunction resetRouter() {\n  const newRouter = createRouter();\n  router.matcher = newRouter.matcher; // 重置路由，不同用户菜单权限会有所不同，在切换用户时，会出现菜单错乱的情况\n}\n// 重新定义 router.go 方法\nconst originalGo = _vueRouter.default.prototype.go;\n_vueRouter.default.prototype.go = function (n, closeTag = true, reload = false) {\n  if (_vue.default.config.layoutType === 'pc') {\n    if (n === -1 && window.previousRoute.path === '/') {\n      if (closeTag) {\n        _bus.default.$emit('DELTAB', true);\n      }\n      // 当前路由没有匹配项，执行返回首页的操作\n      this.replace({\n        path: window.previousRoute.path\n      });\n    } else {\n      if (closeTag) {\n        _bus.default.$emit('DELTAB', false, this.history.current, reload);\n        originalGo.call(this, n);\n      }\n    }\n  } else {\n    originalGo.call(this, n);\n  }\n};\n// 重新定义 router.push 方法\nconst originalPush = _vueRouter.default.prototype.push;\n_vueRouter.default.prototype.push = function push(location) {\n  const paramsKeys = Object.keys(location.params || {});\n  if (paramsKeys.length > 0) {\n    if (location.name) {\n      sessionStorage.setItem(location.name + '_session_params', JSON.stringify(location.params));\n    }\n    if (location.path) {\n      sessionStorage.setItem(location.name + '_session_params', JSON.stringify(location.params));\n    }\n  }\n  // 调用原始的 push 方法\n  return originalPush.call(this, location).catch(err => err);\n};\nvar _default = exports.default = router;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}