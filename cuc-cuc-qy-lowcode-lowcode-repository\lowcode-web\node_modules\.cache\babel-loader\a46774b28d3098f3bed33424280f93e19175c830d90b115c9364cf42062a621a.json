{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.generateRroutes = generateRroutes;\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.iterator.reduce.js\");\nvar _BasicLayout = _interopRequireDefault(require(\"@admin/layouts/BasicLayout\"));\nvar _EmptyLayout = _interopRequireDefault(require(\"@admin/layouts/EmptyLayout\"));\nvar _ParentView = _interopRequireDefault(require(\"@/components/ParentView\"));\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _routers = _interopRequireDefault(require(\"@/router/routers\"));\nvar _path = _interopRequireDefault(require(\"path\"));\nvar _auth = require(\"./auth\");\nvar _validate = require(\"@/utils/validate\");\nvar _svgs = _interopRequireDefault(require(\"@/assets/icons/svgs\"));\n// 是数组\nconst isArr = param => Array.isArray(param);\n\n// 异步挂载组件\nconst loadView = view => {\n  return reslove => require([`@admin/views/${view}`], reslove, e => {\n    console.log('内部组件获取不到：', e);\n    reslove(require('@admin/views/features/noview.vue'));\n  });\n};\n\n// 给接口数据中的component字段挂载真正的组件\nconst mountRouteComponent = routers => {\n  const result = [];\n  routers.forEach(item => {\n    const obj = {\n      ...item\n    };\n    if (item.component) {\n      if (item.component === 'Layout') {\n        obj.component = _BasicLayout.default;\n      } else if (item.component === 'ParentView') {\n        obj.component = _ParentView.default;\n      } else {\n        obj.component = loadView(item.component);\n      }\n    }\n    if (item.children && item.children.length) {\n      obj.children = mountRouteComponent(item.children);\n    }\n    result.push(obj);\n  });\n  return result;\n};\n\n// 获取第一个路由地址作为默认地址\nfunction getRootPath(mountedSource) {\n  // 递归拼接所有路径\n  const ret = function run(current, parentPath = '') {\n    if (!current[0]) return '';\n    const account = _path.default.resolve(parentPath, current[0].path);\n    return isArr(current[0].children) ? run(current[0].children, account) : account;\n  }({\n    ...mountedSource\n  });\n  console.log('默认路径为：', ret);\n  // 如果是外链返回undefined给redirect,表示不重定向\n  if (!ret || (0, _validate.isExternal)(ret) || (0, _validate.isExternal)(ret.slice(1))) {\n    return undefined;\n  } else {\n    return ret;\n  }\n}\n\n// 获取无菜单提示页路由\nfunction getNoMenuRoute() {\n  return {\n    path: '/',\n    redirect: '/nomenu',\n    component: _BasicLayout.default,\n    children: [{\n      path: 'nomenu',\n      name: 'static-nomenu',\n      meta: {\n        title: '提示'\n      },\n      component: loadView('features/nomenu.vue')\n    }]\n  };\n}\n/**\n * icon替换\n * @param {*} i item\n */\nconst iconFix = i => {\n  if (i.meta && i.meta.icon) i.meta.icon = _svgs.default[i.meta.icon] || _svgs.default['m-menu'];\n};\n/**\n * 解析跳转路由地址\n * @param {*} child 子路由\n * @param {*} parent 父路由\n */\nconst resolvePath = (child, parentPath) => {\n  // console.log(child, parentPath);\n  if ((0, _validate.isExternal)(child.path)) {\n    if (child.meta) child.meta.target = '_brank';else child.meta = {\n      target: '_brank'\n    };\n    // 兼容新老版本框架外链方式\n    // 老版本\n    if (/\\?staffId=$/.test(child.path)) child.path = child.path + _store.default.getters.user.staffId + '&token=' + (0, _auth.getToken)() + '&refreshToken=' + (0, _auth.getRefreshToken)();\n    if (/\\?token=$/.test(child.path)) child.path = child.path + (0, _auth.getToken)() + '&refreshToken=' + (0, _auth.getRefreshToken)();\n    // 新版本\n    if (child.path.indexOf('http') != -1 && child.path.indexOf('?') != -1) {\n      const objData = {\n        ..._store.default.getters.user,\n        token: (0, _auth.getToken)(),\n        refreshToken: (0, _auth.getRefreshToken)()\n      };\n      // 第一种方法：for...in循环\n      for (var key in objData) {\n        // 去除空格\n        const delSpace = child.path.replace(/\\s+/g, '');\n        if (delSpace.indexOf('${' + key + '}') != -1) {\n          const str = delSpace.replace('${' + key + '}', objData[key]);\n          child.path = str;\n        }\n        // if (child.name==\"putOnDataListing\") {\n        //   child.query = child.meta.id\n        // }\n      }\n    }\n  } else {\n    child.path = _path.default.resolve(parentPath ? parentPath : '', child.path);\n  }\n};\n\n// 生成菜单所需数据\nfunction parseForAntdMenu(mountedSource, parentPath) {\n  const result = [];\n  mountedSource.forEach(item => {\n    if (item.hidden) return;\n    const obj = {\n      ...item\n    };\n    resolvePath(obj, parentPath);\n    iconFix(obj);\n    if (isArr(obj.children)) {\n      obj.children = parseForAntdMenu(obj.children, obj.path);\n    }\n    result.push(obj);\n  });\n  return result;\n}\nfunction flatSpecialMenu(source) {\n  return source.reduce((p, c) => {\n    if (!c.meta && isArr(c.children) && c.children.length === 1) p.push(c.children[0]);else p.push(c);\n    if (isArr(c.children)) flatSpecialMenu(c.children);\n    return p;\n  }, []);\n}\n\n/**\n * 菜单生成器\n * @param {*} source 接口菜单\n */\nfunction generateMenus(mountedSource) {\n  const menuSource = parseForAntdMenu(mountedSource);\n  const fixedMenuSource = flatSpecialMenu(menuSource);\n  console.log('sourceMenu:', mountedSource);\n  console.log('fixedMenu:', fixedMenuSource);\n  _store.default.dispatch('SetSidebarRouters', fixedMenuSource);\n}\n\n/**\n * 路由生成器\n * @param {*} source 接口菜单\n */\nfunction generateRroutes(source, next, to) {\n  const mountedSource = mountRouteComponent(source);\n  _store.default.dispatch('GenerateRoutes', mountedSource).then(() => {\n    if (mountedSource.length === 0) {\n      _routers.default.addRoute(getNoMenuRoute());\n    } else {\n      mountedSource.forEach(item => {\n        _routers.default.addRoute(item); // 动态添加可访问路由表\n      });\n    }\n    _routers.default.addRoute({\n      path: '/',\n      redirect: getRootPath(mountedSource),\n      component: _BasicLayout.default\n    });\n    _routers.default.addRoute({\n      path: '*',\n      redirect: '/404'\n    });\n    next({\n      ...to,\n      replace: true\n    });\n  });\n  generateMenus(mountedSource);\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}