{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.filter.js\");\nrequire(\"core-js/modules/es.iterator.find.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\nvar _fillData = require(\"@admin/api/fillData\");\nvar _index = _interopRequireDefault(require(\"@/components/form-render/index\"));\nvar _fillAction = _interopRequireDefault(require(\"@/mixins/fillAction\"));\nvar _fillEdit = _interopRequireDefault(require(\"@/mixins/fillEdit\"));\nvar _own = require(\"@admin/api/formManager/own\");\nvar _utils = require(\"@/components/form-render/utils\");\nvar _tpl = require(\"../../api/formManager/tpl\");\nvar _fillData2 = require(\"../../api/fillData\");\nvar _axios = _interopRequireDefault(require(\"axios\"));\nvar _parseToken = require(\"@/utils/parseToken\");\n// import { saveWriteData, writeDataDetail, autoShowFields,checkEdit } from '@admin/api/fillData'\nvar _default = exports.default = {\n  name: 'fillActionExternal',\n  // mixins:[fillAction,fillEdit],\n  components: {\n    VFormRender: _index.default\n  },\n  data() {\n    const {\n      taskId,\n      taskStaffId = '',\n      writeDataId,\n      disable = 0,\n      queryType = 0,\n      path = ''\n    } = this.$route.query;\n    return {\n      disable: Number(disable) === 1,\n      pageLoading: false,\n      formJson: {},\n      formData: {},\n      taskId,\n      modelId: '',\n      taskStaffId,\n      loading: false,\n      detail: {},\n      writeData: {},\n      handleFn: null,\n      currChangeValue: '',\n      ajaxFormData: {},\n      upUrl: `/fillData/detail/index?taskId=${taskId}&taskStaffId=${taskStaffId}&queryType=${queryType || 0}&pageName=home`,\n      formPage: path,\n      token: '',\n      writeDataId: ''\n    };\n  },\n  created() {},\n  mounted() {\n    console.log(123123);\n    const search = window.location.search || window.location.hash.split('?')[1] || '';\n    this.token = (0, _parseToken.parseParamKey)(search, 'token');\n    this.modelId = (0, _parseToken.parseParamKey)(search, 'modelId');\n    this.writeDataId = (0, _parseToken.parseParamKey)(search, 'writeDataId');\n    this.getFromTaskDetailOut();\n  },\n  methods: {\n    goBack() {\n      if (this.formPage === 'home') {\n        this.$router.replace(this.upUrl);\n      } else {\n        this.$router.go(-1);\n      }\n    },\n    clearForm() {\n      this.$refs['VFormRender'].resetForm();\n    },\n    getFromTaskDetailOut() {\n      this.pageLoading = true;\n      (0, _own.getFromTaskDetailOut)(this.token ? this.modelId : this.taskId).then(res => {\n        if (res.code === '00000') {\n          this.detail = {\n            ...(res?.data || {})\n          };\n          this.formJson = JSON.parse(res?.data?.formJson ?? '{}');\n          this.$refs['VFormRender']?.setFormJson(this.formJson);\n          this.taskStaffId = res?.data?.formTaskStaffList[0].taskStaffId;\n          if (this.writeDataId) {\n            this.$nextTick(() => {\n              this.writeDataDetail();\n            });\n          }\n        }\n      }).finally(() => {\n        this.pageLoading = false;\n      });\n    },\n    writeDataDetail() {\n      this.pageLoading = true;\n      const {\n        widgetList\n      } = this.formJson;\n      let subformKeys = [];\n      let arrList = [];\n      let numList = [];\n      let boolList = [];\n      widgetList.forEach(widget => {\n        if (widget.type === 'common') {\n          console.log(widget);\n          widget.widgetList.forEach(subWidget => {\n            if (subWidget.type === 'sub-form') {\n              subformKeys.push(subWidget.options.name);\n            }\n          });\n          widget.widgetList.forEach(subWidget => {\n            if (subWidget.type === 'checkbox' || subWidget.type === 'time-range' || subWidget.type === 'select' || subWidget.type === 'date-range') {\n              arrList.push(subWidget.options.name);\n            }\n            if (subWidget.type === 'slider') {\n              numList.push(subWidget.options.name);\n            }\n            if (subWidget.type === 'switch') {\n              boolList.push(subWidget.options.name);\n            }\n          });\n        }\n      });\n      console.log(arrList);\n      (0, _fillData.writeDataDetail)({\n        taskStaffId: this.taskStaffId,\n        writeDataId: this.writeDataId\n      }).then(res => {\n        if (res.code === '00000' && res.data.fieldsMap) {\n          const dataTable = JSON.parse(this.detail.dataTable ?? '{}');\n          const detial = this.writeData = {\n            ...(res.data ?? {})\n          };\n          const formObject = this.$refs['VFormRender'].getFormData(false);\n          const subKeys = []; //子表单名称列表\n          console.log(detial);\n          Object.keys(formObject).forEach(key => {\n            // 子表单\n            if (subformKeys.includes(key) || key.indexOf('subform') >= 0) {\n              subKeys.push(key);\n            } else {\n              const {\n                baseFields = [],\n                otherFields = []\n              } = dataTable;\n              const typeObj = [...baseFields, ...otherFields].find(item => item.fieldKey == key);\n              this.formData[key] = typeObj ? (0, _utils.getFormDataParseString)(detial.fieldsMap[key], typeObj) : detial.fieldsMap[key];\n            }\n          });\n          subKeys.forEach((key, index) => {\n            this.formData[key] = [];\n            const subTable = detial.subTables[index];\n            subTable?.fieldsDatas?.forEach(item => {\n              const obj = {};\n              Object.keys(item || {}).forEach(subkey => {\n                const typeObj = dataTable.subTables[index]?.fields?.find(item => item.fieldKey == subkey);\n                obj[subkey] = typeObj ? (0, _utils.getFormDataParseString)(item[subkey], typeObj) : item[subkey];\n              });\n              this.formData[key].push(obj);\n            });\n          });\n          this.formData = this.convertValueToArrayIfKeyExists(this.formData, arrList);\n          this.formData = this.convertValueToNumberIfKeyExists(this.formData, numList);\n          this.formData = this.convertValueToBooleanIfKeyExists(this.formData, boolList);\n          this.$refs['VFormRender'].setFormData(this.formData);\n          setTimeout(() => {\n            if (this.disable) {\n              this.$refs['VFormRender']?.disableForm();\n            }\n          }, 500);\n        }\n      }).finally(() => {\n        this.pageLoading = false;\n      });\n    },\n    convertValueToArrayIfKeyExists(obj, keysArray) {\n      for (let key in obj) {\n        if (obj.hasOwnProperty(key)) {\n          // 如果 key 存在于给定的 keysArray 中\n          if (keysArray.includes(key)) {\n            const value = obj[key];\n\n            // 如果 value 是字符串且包含逗号，则将其拆分成数组\n            if (typeof value === 'string' && value.includes(',')) {\n              obj[key] = value.split(',').map(item => item.trim()); // trim 去除多余空格\n            } else if (!Array.isArray(value)) {\n              // 如果 value 不是数组，则将其转为数组\n              obj[key] = [value];\n            }\n          }\n        }\n      }\n      return obj;\n    },\n    convertValueToNumberIfKeyExists(obj, keysArray) {\n      for (let key in obj) {\n        if (obj.hasOwnProperty(key) && keysArray.includes(key)) {\n          // 将对应的值转换为数字类型\n          obj[key] = Number(obj[key]);\n        }\n      }\n      return obj;\n    },\n    convertValueToBooleanIfKeyExists(obj, keysArray) {\n      for (let key in obj) {\n        if (obj.hasOwnProperty(key) && keysArray.includes(key)) {\n          // 将值转换为数字\n          const numericValue = Number(obj[key]);\n\n          // 将数字值转换为布尔类型：0 -> false, 非0的数字和NaN -> true\n          obj[key] = !isNaN(numericValue) && numericValue !== 0;\n        }\n      }\n      return obj;\n    },\n    submitFn2() {},\n    submitFn(val) {\n      this.$refs['VFormRender'].getFormData().then(formData => {\n        // if (!formData._departmentList) {\n        //   this.$message({\n        //     message: '请选择所属部门',\n        //     type: 'warning',\n        //   });\n        //   return;\n        // }\n\n        //校验表单是否所有字段都是空\n        const writeData = Object.keys(formData).filter(key => formData[key]);\n        //校验表单是否所有字段都是空\n        if (writeData.length === 0) {\n          this.$message({\n            message: '表单数据为空，请核查数据',\n            type: 'info'\n          });\n          return false;\n        }\n        this.$confirm('确认提交数据?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.save(1, () => {});\n          if (this.token) {\n            // 从url中获取taskId、userId、modelId、deptId数据\n            const search = window.location.search || window.location.hash.split('?')[1] || '';\n            const taskId = (0, _parseToken.parseParamKey)(search, 'taskId');\n            const userId = (0, _parseToken.parseParamKey)(search, 'userId');\n            const type = (0, _parseToken.parseParamKey)(search, 'type');\n            (0, _tpl.getModelId)(this.detail.taskName).then(res => {\n              if (res.code === '00000') {\n                if (taskId && userId && res.data.taskId && type) {\n                  const para = {\n                    taskId,\n                    userId,\n                    type,\n                    modelId: res.data.taskId,\n                    taskStaffId: this.taskStaffId\n                  };\n                  (0, _fillData2.getFormFillUrl)(para);\n                  try {\n                    setTimeout(() => {\n                      window.close();\n                    }, 500);\n                  } catch (error) {\n                    setTimeout(() => {\n                      // 跳转\n                      window.location.replace(\"https://video.wgos.top/figure/template\");\n                    }, 500);\n                  }\n                }\n              }\n            });\n          }\n\n          // this.submitFn1('数村政府')\n        }).catch(err => {\n          console.log('取消', err);\n        });\n      });\n    },\n    submitFn1() {\n      this.loading = true;\n      (0, _fillData.writeDataSubmitOut)({\n        taskStaffId: this.taskStaffId\n      }).then(res => {\n        if (res.code === '00000') {\n          this.$message({\n            message: res.msg,\n            type: 'success'\n          });\n          //this.$router.go(-1,true,true)\n          // this.$router.replace('/fillData/list/index')\n        }\n      }).finally(() => {\n        this.loading = false;\n      });\n    },\n    save(tag = 0, callBack) {\n      const {\n        widgetList\n      } = this.formJson;\n      let subformKeys = [];\n      widgetList.forEach(widget => {\n        if (widget.type === 'common') {\n          widget.widgetList.forEach(subWidget => {\n            if (subWidget.type === 'sub-form') {\n              subformKeys.push(subWidget.options.name);\n            }\n          });\n        }\n      });\n      this.$refs['VFormRender'].getFormData().then(formData => {\n        //校验表单是否所有字段都是空\n        const writeData = Object.keys(formData).filter(key => formData[key]);\n        const {\n          fieldsMap,\n          subTables = []\n        } = this.writeData;\n        const params = {\n          taskStaffId: this.taskStaffId,\n          fieldsMap: {\n            ...fieldsMap\n          },\n          subTables: [],\n          writeDataId: this.writeDataId ?? ''\n        };\n        const dataTable = JSON.parse(this.detail.dataTable ?? '{}');\n        //校验表单是否所有字段都是空\n        if (writeData.length === 0) {\n          if (this.formJson.formConfig.layoutType == 'H5') {\n            this.$toast('表单数据为空，请核查数据');\n          } else {\n            this.$message({\n              message: '表单数据为空，请核查数据',\n              type: 'info'\n            });\n          }\n          return false;\n        }\n        this.loading = true;\n        Object.keys(formData).forEach(key => {\n          const formItem = formData[key];\n          // 子表单\n          if (subformKeys.includes(key) || key.indexOf('subform') >= 0) {\n            const fieldsDatas = [],\n              oldfieldsDatas = subTables[params.subTables.length]?.fieldsDatas || [];\n            const len = params.subTables.length;\n            formItem?.forEach((item, index) => {\n              if (oldfieldsDatas[index]?.id) {\n                item.id = oldfieldsDatas[index]?.id || null;\n                item.task_staff_id = oldfieldsDatas[index]?.task_staff_id || null;\n              }\n              Object.keys(item).forEach(subkey => {\n                const typeObj = dataTable.subTables[index]?.fields?.find(item => item.fieldKey == subkey);\n                item[subkey] = (0, _utils.setFormDataToString)(item[subkey], typeObj);\n              });\n              fieldsDatas.push(item);\n            });\n            params.subTables.push({\n              table: dataTable?.subTables[len]?.table || '',\n              fieldsDatas\n            });\n          } else {\n            const {\n              baseFields = [],\n              otherFields = []\n            } = dataTable;\n            const typeObj = [...baseFields, ...otherFields].find(item => item.fieldKey == key);\n            params.fieldsMap[key] = (0, _utils.setFormDataToString)(formItem, typeObj);\n          }\n        });\n        params.fieldsMap.data_status = '1';\n        //   writeDataSubmitOut(params)\n        //     .then(res => {\n        //       if (res.code === '00000') {\n        //         if (tag === 0) {\n        //           if(this.formJson.formConfig.layoutType == 'H5'){\n        //             this.$toast.success('成功')\n        //           }else{\n        //             this.$message({\n        //               message: res.msg,\n        //               type: 'success',\n        //             })\n        //           }\n        //           this.$router.go(-1,true,true)\n        //         } else if(tag === 1) {\n        //           if(this.formJson.formConfig.layoutType == 'H5'){\n        //             this.$toast.success('成功')\n        //           }else{\n        //             this.$message({\n        //               message: res.msg,\n        //               type: 'success',\n        //             })\n        //           }\n        //           this.writeDataId = null\n        //           this.$refs['VFormRender'].resetForm()\n        //         }else if(tag === 2){\n        //           //this.$router.go(-1,true,true)\n        //           callBack&&callBack()\n        //         }\n        //       }\n        //     })\n        //     .finally(() => {\n        //       this.loading = false\n        //     })\n        // })\n        if (tag == 0 && this.token) {\n          (0, _fillData.saveWriteData)({\n            ...params\n          }).then(res => {\n            if (res.code === '00000') {\n              if (this.formJson.formConfig.layoutType == 'H5') {\n                this.$toast.success('成功');\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'success'\n                });\n              }\n              // if(this.formJson.formConfig.layoutType == 'H5'){\n              //   this.$router.replace('/fill/own/detail')\n              // }else{\n              //   this.$router.replace('/fillData/list/index')\n              // }\n              setTimeout(() => {\n                // 跳转\n                window.location.replace(\"https://video.wgos.top/figure/template\");\n              }, 500);\n            }\n          }).finally(() => {\n            this.loading = false;\n          });\n          return;\n        }\n        _axios.default.post('/low-code-manage/formTaskStaff/out/submitData', params).then(res => {\n          if (res.code === '00000') {\n            if (tag === 0) {\n              if (this.formJson.formConfig.layoutType == 'H5') {\n                this.$toast.success('成功');\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'success'\n                });\n              }\n              this.$router.go(-1, true, true);\n            } else if (tag === 1) {\n              if (this.formJson.formConfig.layoutType == 'H5') {\n                this.$toast.success('成功');\n              } else {\n                this.$message({\n                  message: res.msg,\n                  type: 'success'\n                });\n                setTimeout(() => {\n                  // 跳转\n                  window.location.replace(\"https://video.wgos.top/figure/template\");\n                }, 500);\n                return;\n              }\n              this.writeDataId = null;\n              this.$refs['VFormRender'].resetForm();\n            } else if (tag === 2) {\n              //this.$router.go(-1,true,true)\n              callBack && callBack();\n            }\n          }\n        }).finally(() => {\n          this.$toast.success('成功');\n          this.loading = false;\n        });\n      });\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}