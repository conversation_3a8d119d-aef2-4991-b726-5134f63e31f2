<template>
  <div class="app-container">
    <section
      class="mb-2.5"
    >
      <div class="flex items-center justify-between">
        <p class="mb-2 text-base text-black">{{ detail.taskName }}</p>
          <section class="flex justify-center footer">
            <el-button size="small"  type="primary" class="w-28" @click="submitFn" :loading="loading" v-if="queryTypes === 0" :disabled="!tableData.length">提交</el-button>
          </section>
      </div>
    </section>

    <section class="list">
      <el-form  inline size="small" class="form  rounded bg-white p-2.5">
        <el-form-item style="margin-bottom: 0;width: 100%">
          <el-input  placeholder="请输入内容，按enter键确认" class="input-with-select" style="width:100%" v-model="seachVal" @keyup.enter.native="inputFilter">
            <el-tag slot="suffix" style="margin-right: 8px" :key="index" v-for="(tag,index) in searchTags" closable :disable-transitions="false" @close="handleTagClose(tag)">{{tag.fieldName}}: {{tag.value}}</el-tag>
            <el-select slot="prepend" value-key="fieldKey" v-model="selectField" placeholder="请选择" filterable style="width: 100px;">
              <el-option :label="field.fieldName" :value="field" v-for="field in filterFields" :key="field.fieldKey"></el-option>
            </el-select>
            <el-button slot="append" icon="el-icon-search" @click="filterTable"></el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <div>
        <list-layout
          size="mini"
          v-if="columns.length>0"
          ref="list-layout"
          @selection-change="handleSelectionChange"
          :form="form"
          :table="{ columns ,tableButtom:pageTag?undefined:120}"
          :multiple="queryTypes === 0"
          @change="tableDataChange"
          :requestFn="requestFn"
        >
          <template #table-header>
            <div class="">
              <el-button :key="totalCheckList.join(',')" v-show="queryTypes === 0&&tableData.length>0" type="primary" plain size="small" v-confirm-click="{
              handler: confirmClick,
              msg: '确定删除表单？',
              params: {
                dataIds:totalCheckList,
                taskStaffId
              },
            }">批量删除</el-button>
              <el-button v-show="queryTypes === 0" plain size="small" icon="el-icon-upload" @click="dialogFormVisible = true">数据导入</el-button>
              <el-button :disabled="tableData.length==0" type="primary" plain size="small" icon="el-icon-download" @click="downVisible = true">数据导出</el-button>
            </div>
          </template>
          <template #table-column-index="{ scope }">
            <p>{{ scope.$index + 1 }}</p>
          </template>
          <template #table-column-taskId="{ scope }">
            <p class="text-blue-400">{{ scope.row['taskId'] }}</p>
          </template>
          <template #table-column-taskStatus="{ scope }">
            <div>
              <!--          //0-草稿;1-待审核;2-待修改（审批不通过）;3-审批通过;4-数据收集中;5-收集完成;6-流程停止-->
              <template v-for="item in tagOptions">
                <el-tag size="mini" :key="item.value" v-show="scope.row['writeStatus'] == item.value" :type="item.tagType">{{
                    item.label
                  }}</el-tag>
              </template>
            </div>
          </template>
          <template #table-column-action="{ scope }">
            <icon-edit v-if="queryTypes === 0" @click="goPage(scope.row, 'fillActionExternal',0)" />
            <icon-view @click="goPage(scope.row, 'fillActionExternal',1)" />
            <icon-del  v-if="queryTypes === 0"   @click="delForm({
                dataIds:[scope.row.id],
                taskStaffId
              })"/>
          </template>
        </list-layout>
      </div>
    </section>
    <el-dialog title="请选择文件" :visible.sync="dialogFormVisible" width="600px">
      <el-form v-ant-loading size="mini">
        <el-form-item label="所属部门">
          <el-select v-model="depart" placeholder="请选择所属部门" clearable>
            <el-option v-for="item in departs" :key="item.value" :label="item.value" :value="item.value"></el-option>
          </el-select>
          <el-button type="primary" plain size="mini" style="margin-left: 10px" @click="downloadTemplate" :loading="dowloadTmpLoading">模版下载</el-button>
        </el-form-item>
        <el-form-item label="">
          <el-upload  class="upload-demo"
                      drag
                      action=""
                      :show-file-list="true"
                      accept=".xlsx,.xls,csv,.txt"
                      ref="uploadFile1"
                      :file-list="uploadData.file"
                      :on-change="changeFile"
                      :http-request="uploadFile"
                      >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx文件，且不超过20M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
       <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
       <el-button size="small" type="primary" @click="httpRequest">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="数据导出" :visible.sync="downVisible" width="440px">
        <el-form label-suffix=":" label-width="80px">
          <el-form-item label="选择字段">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            <el-checkbox-group v-model="fieldKeys" @change="handleCheckedCitiesChange">
              <el-checkbox class="truncate-checkbox" v-for="item in columnsTwo" :key="item" :label="item.prop" name="type">
                <p class="w-28 truncate-p">
                  <truncate :content="item.label">{{ item.label }}</truncate>
                </p>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="downVisible = false">取 消</el-button>
        <el-button :loading="downLoading" size="small" type="primary" @click="downFn">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const proxyLowCode = process.env.VUE_APP_LOW_API
import { writeStatusOptions } from "@/config"
import { dataFormTemplateDownload,getFromTaskEncryptedKeys } from '@admin/api/fillData'
import listLayout from '@admin/components/Template/list'
import IconEdit from '@admin/components/ElTable/Aciton/icon-edit'
import IconView from '@admin/components/ElTable/Aciton/icon-view'
import { getFromTaskDetail } from '@admin/api/formManager/own'
import { deleteWriteData, writeDataSubmit } from '@admin/api/fillData'
import IconDel from '@/pages/admin/components/ElTable/Aciton/icon-del'
import { uploadExcel} from "@/pages/admin/api/fillData"
import fillEdit from "./mixins/fillEdit"
import request from "@/utils/request"
import { getToken } from "@/utils/auth"
import truncate from "@/pages/admin/components/ElTable/Column/truncate"
import { parseParamKey } from "@/utils/parseToken"

import { queryModelDetailPublished } from '@admin/api/dataModal/dataModel';


export default {
  name: 'fillDataDetail',
  components: {
    IconDel,
    listLayout,
    IconView,
    IconEdit,
    truncate,
  },
  props:{
    pageTag:{
      type:String,
      default:''
    },
    params:{
      type:Object,
      default(){
       return null
      }
    }
  },
  mixins:[fillEdit],
  data() {
    const { taskId, taskStaffId,queryType=0,data='{}',queryTypes } = this.params||this.$route.query
    return {
      modelId:'',
      checkAll: false,
      isIndeterminate: false,
      downLoading:false,
      fieldKeys:[],
      dialogFormVisible:false,
      downVisible:false,
      uploadData:{
        taskId,
        taskStaffId,
        file:[],
      },
      files: [],
      loading:false,
      taskId,
      taskStaffId,
      detail: {},
      queryType:Number(queryType),
      queryTypes:Number(queryTypes),
      tagOptions: [...writeStatusOptions],
      dataStatusOptions:[{
        value: '0',
        label: '正式数据',
        tagType: 'primary',
      },{
        value: '1',
        label: '新增',
        tagType: 'success',
      },{
        value: '2',
        label: '修改',
        tagType: 'warning',
      },{
        value: '3',
        label: '删除',
        tagType: 'danger',
      }],
      form: {
        size: 'small',
        defaultValue: {
          taskStaffId: taskStaffId,
          taskId:taskId,
        },
      },
      columns: [
        {
          label: '序号',
          prop: 'index',
          width: 50,
          fixed: 'left',
          scope: true,
          align: 'left',
        },
        {
          label: '数据所属部门',
          prop: '_departmentList',
          width: 100,
          fixed: 'right',
          scope: true,
          align: 'left',
        },
      ],
      actionUrl: taskStaffId?'/formTaskStaff/writeDataPage':'/formTaskStaff/formWriteDataPage',
      checkList:{},
      tableData:[],
      dowloadTmpLoading:false,
      subformKeys:[],
      selectField: {},
      seachVal: '',
      searchTags: [],
      filterFields:[],
      depart: '',
      departs: [],
      noEncryptColumns:[],
      token:'',
    }
  },
  computed:{
    totalCheckList(){
      let list = []
      Object.keys(this.checkList).forEach(key=>{
       const sulist = this.checkList[key]
        sulist?.forEach(val=>{
         list.push(val.id)
       })
      })
      return list
    },
    columnsTwo(){
      if(this.columns.length>0){
        const list = ['index','write_staff_id','updatedAt','action']
        return [...this.columns,...this.subformKeys].filter(item=>!list.includes(item.prop))
      }else {
        return this.columns
      }
    },
  },
  created(){

    this.$store.state.user.userDeparts.map(item=>{
      this.departs.push({
        lable:item.orgName,
        value:item.orgName,
      })
    })
    if (this.$store.state.user.userDeparts.length ==1) {
      this.depart = this.$store.state.user.userDeparts[0].orgName
    }
    this.getFromTaskEncryptedKeys()
  },
  mounted() {
    const search = window.location.search || window.location.hash.split('?')[1] || '';
    this.token = parseParamKey(search,'token')
    this.modelId = parseParamKey(search,'modelId')
    if (this.modelId) {
      this.getFromTaskDetail()
    }
    if(this.queryType === 2){

    }
  },
  methods: {
    getFromTaskEncryptedKeys(){
      getFromTaskEncryptedKeys(this.modelId).then(res=>{
        this.noEncryptColumns = res.data
      })
    },
    requestFn(param){
                let EncryptedDatas = JSON.parse(sessionStorage.getItem('EncryptedDatas')) ?
    JSON.parse(sessionStorage.getItem('EncryptedDatas'))[this.getAllUrlParams(window.location.href).modelId]:[]
      const formData = this.$refs["list-layout"].formInline
      const params = {
        pageNum: param.pageNum,
        pageSize: param.pageSize,
        ...formData,
        params:{
          where:[],
        },
          encrypt: 1,
          noEncryptColumns:EncryptedDatas || [],

      }
      this.searchTags.forEach(item=>{
        let obj = {
          key: item.fieldKey,
          rel:'search',
          val: item.value
        }
        params.params.where.push(obj)
      })
      if (this.queryType == '3') {
        params.params.where.push({
            key: 'data_status',
            rel:'search',
            val: '0,1,2'
        })
      } else if(this.queryType == '1') {
        params.params.where.push({
            key: 'data_status',
            rel:'search',
            val: '1,2,3'
        })
      } else {
        params.params.where.push({
            key: 'data_status',
            rel:'search',
            val: '0,1,2,3'
        })
      }


      return new Promise((resolve, reject)=>{
        request({
          url: `${this.actionUrl}?pageNum=${params.pageNum}&pageSize=${params.pageSize}`,
          method: 'post',
          data:params,
          baseURL: proxyLowCode,
        }).then(res=>{
          resolve(res)
        }).catch(err=>{
          reject(err)
        })
      })
    },
    getAllUrlParams(urls) {
    var url = urls || location.href
    // 用JS拿到URL，如果函数接收了URL，那就用函数的参数。如果没传参，就使用当前页面的URL
    var queryString = url ? url.split('?')[1] : window.location.search.slice(1);
    // 用来存储我们所有的参数
    var obj = {};
    // 如果没有传参，返回一个空对象
    if (!queryString) {
        return obj;
    }
    // stuff after # is not part of query string, so get rid of it
    queryString = queryString.split('#')[0];
    // 将参数分成数组
    var arr = queryString.split('&');
    for (var i = 0; i < arr.length; i++) {
        // 分离成key:value的形式
        var a = arr[i].split('=');
        // 将undefined标记为true
        var paramName = a[0];
        var paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
        if (paramName.match(/\[(\d+)?\]$/)) {
            // 如果paramName不存在，则创建key
            var key = paramName.replace(/\[(\d+)?\]/, '');
            if (!obj[key]) obj[key] = [];
            // 如果是索引数组 e.g. colors[2]
            if (paramName.match(/\[\d+\]$/)) {
                // 获取索引值并在对应的位置添加值
                var index = /\[(\d+)\]/.exec(paramName)[1];
                obj[key][index] = paramValue;
            } else {
                // 如果是其它的类型，也放到数组中
                obj[key].push(paramValue);
            }
        } else {
            // 处理字符串类型
            if (!obj[paramName]) {
                // 如果如果paramName不存在，则创建对象的属性
                obj[paramName] = paramValue;
            } else if (obj[paramName] && typeof obj[paramName] === 'string') {
                // 如果属性存在，并且是个字符串，那么就转换为数组
                obj[paramName] = [obj[paramName]];
                obj[paramName].push(paramValue);
            } else {
                // 如果是其它的类型，还是往数组里丢
                obj[paramName].push(paramValue);
            }
        }
    }
    return obj;
},
    handleTagClose(tag) {
      this.searchTags = this.searchTags.filter(val => val.fieldKey !== tag.fieldKey);
    },
    filterTable() {
      let obj = {

      }
      this.filterFields.forEach(({
                                   fieldKey
                                 })=>{
        if(this.$refs['list-layout'].formInline.hasOwnProperty(fieldKey)){
          delete this.$refs['list-layout'].formInline[fieldKey]
        }
      })
      this.searchTags.forEach(item=>{
        obj[item.fieldKey] =  item.value
      })
      this.$refs['list-layout'].formInline = {
        ... this.$refs['list-layout'].formInline,
        ...obj
      }
      this.$refs['list-layout'].getTableData()
    },
    inputFilter() {
      if (!this.selectField.fieldKey) {
        this.$message.warning('请先选择要搜索的字段');
        return;
      }
      const index = this.searchTags.findIndex(item=>item.fieldKey === this.selectField.fieldKey)

      const obj = {};
      obj.fieldName = this.selectField.fieldName;
      obj.fieldKey = this.selectField.fieldKey;
      obj.value = this.selectField.dataType === '3' ? Number(this.seachVal) : this.seachVal;
      if(index>=0){
        this.searchTags[index] = {
          ...obj
        }
      }else{
        this.searchTags.push(obj);
      }
      this.selectField = {};
      this.seachVal = '';
      this.filterTable();
    },
    handleCheckedCitiesChange(value){
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.columnsTwo.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.columnsTwo.length;
    },
    handleCheckAllChange(val){
      this.fieldKeys = val ? this.columnsTwo.map(item=>item.prop) : [];
      this.isIndeterminate = false;
    },
    changeFile(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      this.uploadData.file = fileList;
    },
    //上传文件的事件
    uploadFile(item){
      this.$message({
    		type:'warning',
    		message:'文件上传中......'
    	});
      this.files.push(item.file);//成功过后手动将文件添加到展示列表里
    },
    //请求
    httpRequest() {
      if (this.depart && this.files.length>0) {
        let params = new FormData()
        params.append("_departmentList",this.depart)
        params.append("modelId",this.modelId)
        params.append("taskStaffId",this.taskStaffId)
        params.append("file",this.files[0])
        uploadExcel(params).then(res=>{
          if(res.code === '00000'){
            this.$message.success(res.msg)
            this.$refs['list-layout'].getTableData()
            setTimeout(()=>{
              this.dialogFormVisible = false
            },500)
          }else{
            this.$message.success(res.msg)
          }
        })
      } else {
        if (!this.depart) {
          this.$message({
            message: '所属部门不能为空！',
            type: 'warning'
          });
        } else {
          this.$message({
            message: '文件不能为空！',
            type: 'warning'
          });
        }

      }

    },
    downloadTemplate(){
      this.dowloadTmpLoading = true;
      dataFormTemplateDownload({
        modelId:this.modelId,
        taskStaffId:this.taskStaffId,
        taskName:this.detail.taskName
      }).finally(()=>{
        this.dowloadTmpLoading = false;
      })
    },
    tableDataChange(val=[]){
      this.tableData = [...val]
    },
    handleSelectionChange(val) {
      const pageNum = this.$refs['list-layout'].formInline.pageNum
      this.$set(this.checkList,pageNum,val)
    },
    getFromTaskDetail() {
      this.pageLoading = true
      getFromTaskDetail(this.modelId)
        .then(res => {
          if (res.code === '00000') {
            this.detail = {
              ...(res.data ?? {}),
            }
            const dataTable = JSON.parse(this.detail.dataTable ?? '{}')
            dataTable.subTables.forEach(item=>{
              item.fields.forEach(val=>{
                this.subformKeys.push({
                  label: val.fieldName,
                  prop: val.fieldKey,
                })
              })
            })
            this.columns = [{
              label: '序号',
              prop: 'index',
              width: 50,
              fixed: 'left',
              scope: true,
              align: 'left',
            }]
            // console.log(dataTable);
            dataTable.baseFields.forEach(val => {
              if(val.dataType!=='7'&&val.fieldName){
                this.columns.push({
                  label: val.fieldName,
                  prop: val.fieldKey,
                  encrypted: this.noEncryptColumns.indexOf(val.fieldKey) !== -1 ? 1 :  0,
                  width: 180,
                  fixed: false,
                  scope: false,
                  align: 'left',
                })
              }
            })
            dataTable.otherFields.forEach(val => {
              if(val.dataType!=='7'&&val.fieldName){
                this.columns.push({
                  label: val.fieldName,
                  prop: val.fieldKey,
                  encrypted: this.noEncryptColumns.indexOf(val.fieldKey) !== -1 ? 1 :  0,
                  width: 180,
                  fixed: false,
                  scope: false,
                  align: 'left',
                })
              }
            })
            //添加其他字段
            if(!this.taskStaffId){
              this.columns.push({
                label: '填报人',
                prop: 'write_staff_id',
                width: 'auto',
                minWidth: '200px',
                fixed: false,
                scope: false,
                align: 'left',
              })
            }
            this.columns.push({
              label: '数据状态',
              prop: 'data_status',
              width: 'auto',
              minWidth: '200px',
              fixed: false,
              scope: false,
              align: 'left',
            })
            this.columns.push({
              label: '数据所属部门',
              prop: '_departmentList',
              width: 'auto',
              minWidth: '200px',
              fixed: false,
              scope: false,
              align: 'left',
            })

            this.columns.push({
              label: '更新时间',
              prop: 'updatedAt',
              width: 'auto',
              minWidth: '200px',
              fixed: false,
              scope: false,
              align: 'left',
            })
            //添加操作
            this.columns.push({
              label: '操作',
              prop: 'action',
              width: 140,
              fixed: 'right',
              scope: true,
              align: 'left',
            })

            this.columns.forEach(item=>{
              if(item.prop!=='updatedAt'&&item.prop!=='index'&&item.prop!=='action'){
                this.filterFields.push({
                  fieldName:item.label,
                  fieldKey:item.prop
                })
              }
            })
            console.log('this.filterFields',this.filterFields)
          }
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    goPage(row, path,queryType) {
      console.log(row, path,queryType);
      // window.open(`https://${window.location.host}/lowcode-pc/fillActionExternal?modelId=${this.taskId}&taskStaffId=${this.taskStaffId||row.task_staff_id}&writeDataId=${row.id}&token=${this.token}&queryType=${queryType}&disable=${queryType}&dataStatus=2&data=${row}`)
      window.open(`https://${window.location.host}/lowcode-pc/fillActionExternal?modelId=${this.modelId}&taskStaffId=${this.taskStaffId||row.task_staff_id}&writeDataId=${row.id}&token=${this.token}&queryType=${queryType}&disable=${queryType}&dataStatus=2&data=${row}`)
      // this.$router.replace({
      //   path: `/fillData/${path}/index`,
      //   query: {
      //     modelId: this.modelId,
      //     taskStaffId: this.taskStaffId||row.task_staff_id,
      //     writeDataId: row.id,
      //     queryType,
      //     disable:queryType,
      //     dataStatus:2,
      //     data:row,
      //     token:this.token,
      //   },
      // }, () => {
      //   // 手动处理守卫逻辑
      //   this.$nextTick(() => {
      //     // 强制更新组件
      //   })
      // })
    },
    async delForm(params){
      console.log(params);
      this.$confirm('确定删除表单？', '', {
          distinguishCancelAndClose: true,
          type: 'warning',
          confirmButtonText: '删除',
          cancelButtonText: '取消'
        })
          .then(async () => {
          await deleteWriteData({
            ...params,
            dataIds:params.dataIds.length==0?null:params.dataIds
          }).then(res=>{
            if(res.code==='00000'){
              this.$message({
                message: res.msg,
                type: 'success',
              })
              this.$refs['list-layout'].getTableData()
              this.checkList = []
            }
          })
          })
          .catch(action => {
          });

    },
    async confirmClick(params){
       await deleteWriteData({
         ...params,
         dataIds:params.dataIds.length==0?null:params.dataIds
       }).then(res=>{
         if(res.code==='00000'){
           this.$message({
             message: res.msg,
             type: 'success',
           })
           this.$refs['list-layout'].getTableData()
           this.checkList = []
         }
       })
    },
    submitFn(){
      this.loading = true
      writeDataSubmit({
        taskStaffId: this.taskStaffId,
      }).then(res=>{
        if(res.code==='00000'){
          this.$message({
            message: res.msg,
            type: 'success',
          })
          this.$router.go(-1,true,true)
        }
      }).finally(()=>{
        this.loading = false
      })
    },
    downFn(){
      let EncryptedDatas = JSON.parse(sessionStorage.getItem('EncryptedDatas')) ?
    JSON.parse(sessionStorage.getItem('EncryptedDatas'))[this.getAllUrlParams(window.location.href).modelId]:[]
      if(this.fieldKeys.length>0){
        this.mGet('/low-code-manage/formTask/writeStaff/dataFormDownload',{
          modelId:this.modelId,
          taskStaffId:this.taskStaffId||'',
          fieldKeys:this.fieldKeys,
          noEncryptColumns:EncryptedDatas || [],
        },this.detail.taskName)
      }else{
        this.$message.info('请选择字段')
      }
    },
    mGet(url,params={},fileName) {
      this.downLoading = true;
      let formData = new FormData();
      Object.keys(params).forEach(key=>{
        formData.append(key, params[key]);
      })
      const jsonString = JSON.stringify(Object.fromEntries(formData));
      let objData = JSON.parse(jsonString)
      objData.noEncryptColumns = JSON.parse(jsonString).noEncryptColumns.split(",")
      objData.fieldKeys = JSON.parse(jsonString).fieldKeys.split(",")
      console.log(objData);
      request({
        url,
        method: 'post',
        responseType: 'blob', // 设置响应类型为 blob
        headers:{
          Authorization:getToken(),
          'Content-Type': 'application/json', // 根据需要设置 Content-Type
        },
        data:objData
      }).then(response => {
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([response], {type: 'application/vnd.ms-excel'}), fileName)
        }else{
          let url = window.URL.createObjectURL(new Blob([response]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName+'.xlsx')
          document.body.appendChild(link)
          link.click()
        }
      })
        .catch(error => {
          console.error('下载文件时出错:', error);
        }).finally(()=>{
        this.downLoading = false;
        this.downVisible = false
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  background-color: transparent;
}
::v-deep .el-dialog__header {
  font-style: normal !important;
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
}
::v-deep .el-dialog__footer {
  border-top: 1px solid #ebeef5;
  padding: 10px 20px;
}
::v-deep .el-dialog__headerbtn{
  top:10px;
}
::v-deep .el-dialog__title {
  font-size: 14px;
}
.upload-demo{
  text-align: center;
  //::v-deep .el-upload-list{
  //  display: none;
  //}
}
.truncate-p{
  margin-bottom: 0;
  ::v-deep p{
    margin-bottom: 0;
  }
}
.truncate-checkbox{
  display: inline-flex;
  align-items: center;
  margin-bottom: 5px;
  margin-right: 6px;
}
::v-deep .el-form-item__content{
  width: 100%;
}
::v-deep .el-table__cell{
  padding: 4px 0 !important;
  line-height: 18px !important;
  height: 30px !important;
}
</style>
