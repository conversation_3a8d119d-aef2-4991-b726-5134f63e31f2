import router, { resetRouter } from './routers'
import store from '@admin/store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // getToken from cookie
import { buildMenu,published, pageMyApply} from '@admin/api/system/menu'
import Cookies from 'js-cookie'
import { generateRroutes } from '@/utils/routeAndMenu'
import { queryUrlToken } from '../api/fillData';
import { setToken } from '@/utils/auth';
import { parseParamKey } from '@/utils/parseToken'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login','/403','/fillActionExternal'] // no redirect whitelist

// 修改或扩展路由参数
function ovNext(to,from,next){
  const localData = sessionStorage.getItem(to.name+'_session_params')||sessionStorage.getItem(to.path+'_session_params')
  if(localData){
    if(Object.keys(to.params||{}).length>0){
      next()
    }else{
      // 构建一个新的路由对象，避免直接修改 to 对象
      const newTo = {
        ...to,
        params: JSON.parse(localData),
        repalce:to.path == from?.path?.replace('/redirect','')
      };
      next(newTo)
    }
  }else{
    next()
  }
}
let isTokenHandled = false; // 全局变量，标记是否已处理 token
router.beforeEach((to, from, next) => {
  if (isTokenHandled) {
    next(); // 如果已经处理过 token，直接放行
    return;
  }
  // console.log(to, from, next);
  const search = window.location.search || window.location.hash.split('?')[1] || '';
  const paramsUrl = new URLSearchParams(search);
  debugger
  const token = paramsUrl.get('token')?paramsUrl.get('token'):parseParamKey(search, 'token');
  debugger
  if (token) {
    isTokenHandled = true; // 标记为已处理
    queryUrlToken({token, mode: 2}).then(res=>{
      console.log("🚀 ~ res:", res)
      setToken(res.data.accessToken)
      sessionStorage.setItem('user_info', JSON.stringify(res.data.userInfo))
      const url = window.location.pathname.replace('/lowcode-pc', '') + window.location.search
      next(url) // 否则全部重定向到登录页
      // debugger
    })


    "?modelId=20250828181106570&amp;taskStaffId=1961008778817798145&amp;queryType=3&amp;queryTypes=0&amp;token=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImJmZDQzZjRlLTQ4Y2ItNDhhYi1hMzFiLWYzY2EwNDMxMjExYyJ9.jzpw88cp_Ia_4Dh5KGcbUUigiPS75ExDE63E4nhD47owp0Q15jw-FeAtJeXfITvygBCIki8jnet75-lHsfKoDw"
"?modelId=20250827205713105&taskStaffId=1960688110079250433&writeDataId=1960689672893698050&queryType=0&disable=0&dataStatus=2&data=%5Bobject%20Object%5D&token=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImNhNjY1MzNmLWRiYjMtNDM4MS05MjUzLTE5NWJhNjgzMzc1MyJ9.hvcnje74Ys9VrIDKMnwbtb3HD0AvVJJ0YzwhrT3j8Sri5H91Hr4pc4VMsuMrkpHiEicuRM-SopFztB1aSgy2tw"
    return;
  }

  window.previousRoute = from
  if (to.meta.title) {
    document.title = Cookies.get('systemName')
      ? Cookies.get('systemName') + ' - ' + to.meta.title
      : '开发框架' + ' - ' + to.meta.title
  }
  NProgress.start()
  // //链接 /lowcode-pc/?authCode=xxxx
  if(to.query.authCode&&to.path!=='/login'){
    const path = '/'
    next(`/login?redirect=${path}&authCode=${to.query.authCode}`) // 重定向到登录页
    NProgress.done() // finish progress bar
  }else{
    console.log(to.path);
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      ovNext(to,from,next)
    } else if (getToken()) {
      // 已登录且要跳转的页面是登录页
      if (to.path === '/login') {
        next({ path: '/' })
        NProgress.done()
      } else {
        if (store.getters.roles.length === 0) {
          // 判断当前用户是否已拉取完user_info信息
          store.dispatch('GetInfo').then(() => {
            loadMenus(next, to)
          })
        } else if (!store.getters.permission_routesLoaded) {
          loadMenus(next, to)
        } else {
          ovNext(to,from,next)
        }
      }
    } else {
      /* has no token*/
      // 在模块中调用重置路由
      resetRouter()
      // 清除tab标签缓存
      store.dispatch('tagsView/delAllViews')
      const path = '/'
      next(`/login?redirect=${path}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

export const loadMenus = (next, to) => {
  let buildMenus = []
  buildMenu().then(res => {
    buildMenus = res.data
    published().then(ress=>{
      if (ress.code=='00000') {
        buildMenus.map(item=>{
          if (item.path=='/information') {
            item.children.map(i=>{
              if (i.path=='putOn') {
                ress.data.list.map(item=>{
                  i.children.push({
                      "name": "putOnDataListing"+item.dataId,
                      "path": "/putOn/dataListing?dataId="+item.dataId+'&taskId='+item.taskId,
                      "hidden": false,
                      "redirect": null,
                      "component": "putOn/dataListing",
                      "alwaysShow": null,
                      "meta": {
                          "title": item.dataName,
                          "taskId": item.dataId,
                          "icon": "m-menu",
                          "noCache": true,
                          "iFrame": "1",
                          "closeConfirm": "0",
                          "selectedFields":item.fields
                      },
                      "children": null
                  })
                })
              }
            })
          }
        })
        generateRroutes(buildMenus, next, to)
      }
    })
    pageMyApply({"auditStatus":"0","dataId":"","dataName":"","applyTime":"","auditTime":"","applyStatus":"3","pageNum":1,"pageSize":10,"orderType":"","orderBy":"","applicationId":"1660471189374717953"})
    .then(ress=>{
      if (ress.code=='00000') {
        buildMenus.map(item=>{
          if (item.path=='/information') {
            item.children.map(i=>{
              if (i.path=='myDataApply') {
                const uniqueArr = ress.data.list.filter((value, index, self) =>
                  index === self.findIndex((t) => (
                    t.dataId === value.dataId
                  ))
                );
                uniqueArr.map(item=>{
                  i.children.push({
                      "name": "formDataApply"+item.dataId,
                      "path": "/myDataApply/dataListing?dataId="+item.dataId+'&taskId='+item.taskId+'&applyId='+item.applyId,
                      "hidden": false,
                      "redirect": null,
                      "component": "myDataApply/dataListing",
                      "alwaysShow": null,
                      "meta": {
                          "title": item.dataName,
                          "taskId": item.dataId,
                          "icon": "m-menu",
                          "noCache": true,
                          "iFrame": "1",
                          "closeConfirm": "0",
                          "selectedFields": item.selectedFields,
                      },
                      "children": null
                  })
                })
              }
            })
          }
        })
        generateRroutes(buildMenus, next, to)
      }
    })
  })




}

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
