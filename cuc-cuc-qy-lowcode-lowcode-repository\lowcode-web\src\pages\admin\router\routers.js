import Vue from 'vue'
import Router from 'vue-router'
import BasicLayout from '../layouts/BasicLayout.vue'
import DataModal from '@admin/views/dataModal/dataModal.vue'
import ModalDetail from '@admin/views/dataModal/modalDetail.vue'
import dataBackend from '@admin/views/dataModal/dataBackend/dataBackend.vue'
import dataManage from '@admin/views/dataModal/dataBackend/dataManage.vue'
import addData from '@admin/views/dataModal/dataBackend/data/add.vue'
import editData from '@admin/views/dataModal/dataBackend/data/edit.vue'
import viewData from '@admin/views/dataModal/dataBackend/data/detail.vue'
import personal from '../views/information/personal.vue'
import  listLayout  from '../components/Template/list.vue'
import  statisticalAnalysis  from '@admin/views/information/statisticalAnalysis.vue'
import  fillActionExternal  from '@admin/views/fillData/actionExternal.vue'
import  templateManagement  from '@admin/views/information/templateManagement.vue'
import Event from '@/utils/bus'
// import bigscreen from '@admin/views/bigscreen/index.vue'

Vue.use(Router)

export var constantRouterMap = [
  {
    path: '/login',
    name: 'login',
    meta: { title: '登录', noCache: true },
    component: resolve => require(['@admin/views/login'], resolve),
    hidden: true,
  },
  {
    path: '/404',
    name: 'static-404',
    component: resolve => require(['@admin/views/features/404'], resolve),
    hidden: true,
  },
  {
    path: '/401',
    name: 'static-401',
    component: resolve => require(['@admin/views/features/401'], resolve),
    hidden: true,
  },
  {
    path: '/403',
    name: 'static-403',
    component: resolve => require(['@admin/views/features/403'], resolve),
    hidden: true,
  },
  {
    path: '/redirect',
    name: 'static-redirect',
    component: BasicLayout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: resolve => require(['@admin/views/features/redirect'], resolve),
      },
    ],
  },
  {
    path: '/statisticalAnalysis',
    name: 'statisticalAnalysis',
    component: statisticalAnalysis,
    meta:{
      title: '数据统计分析', noCache: true,
    }
  },
  {
    path: '/fillActionExternal',
    name: 'fillActionExternal',
    component: fillActionExternal,
    meta:{
      title: '外部填报', noCache: true,
    }
  },

    {
    path: '/fillData/actionExternalDetail/index',
    name: 'resolve',
    component: resolve => require(['@admin/views/fillData/actionExternalDetail'], resolve),
    hidden: true,
  },
  // {
  // path: '/',
  // component: BasicLayout,
  // name: 'static-home',
  // redirect: '/dashboard/index',
  // },

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/dashboard',
  //   // hidden: true,
  //   children: [
  //     {
  //       path: 'dashboard',
  //       component: (resolve) => require(['@admin/views/home'], resolve),
  //       name: 'Dashboard',
  //       meta: { title: '首页', icon: 'home-page', affix: true, noCache: true }
  //     }
  //   ]
  // },

  {
    path: '/user',
    name: 'static-user',
    component: BasicLayout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: resolve => require(['@admin/views/system/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' },
      },
    ],
  },

  {
    path: '/tool/gen-edit',
    component: BasicLayout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@admin/views/tools/codeGen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' },
      },
    ],
  },
  {
    path: '/formManager/own/add',
    name: 'ownFormAdd',
    component: resolve => require(['@admin/views/formManager/own/add'], resolve),
    hidden: true,
  },
  // {
  //   path: '/formManager/formAllDetail',
  //   name: 'formManagerFormAllDetail',
  //   component: resolve => require(['@admin/views/formManager/formAllDetail'], resolve),
  //   hidden: true,
  // },
  {
    path: '/formManager/own/edit',
    name: 'ownFormEdit',
    component: resolve => require(['@admin/views/formManager/own/edit'], resolve),
    hidden: true,
  },
  {
    path: '/processManager/add',
    name: 'processAdd',
    component: resolve => require(['@admin/views/processManager/add'], resolve),
    hidden: true,
  },
  {
    path: '/fillData/action/index',
    name: 'fillAction',
    component: resolve => require(['@admin/views/fillData/action'], resolve),
    hidden: true,
  },
  {
    path: '/processManager/edit',
    name: 'processEdit',
    component: resolve => require(['@admin/views/processManager/edit'], resolve),
    hidden: true,
  },
  // 本地路由
  // {
  //   path: '/dataModel',
  //   name: 'dataModel',
  //   component: DataModal,
  //   meta: {
  //     title: '数据模型',
  //   }
  // },
  // {
  //   path: '/fieldManage',
  //   name: 'fieldManage',
  //   component: resolve => require(['@admin/views/fieldManage/index'], resolve),
  //   hidden: true,
  // },
  // {
  //   path: '/dataModel',
  //   name: 'dataModel',
  //   component: DataModal,
  //   meta: {
  //     title: '数据模型',
  //   }
  // },
  {
    path: '/basicSheet',
    name: 'basicSheet',
    component: BasicLayout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: '/modelDetail',
        name: 'modalDetail',
        component: ModalDetail,
        meta: {
          title: '数据模型',
          closeConfirm:'0'
        },
      },
      {
        path: '/dataBackend',
        name: 'dataBackend',
        component: dataBackend,
        meta: {
          title: '数据管理后台',
          closeConfirm:'0'
        },
      },
      {
        path: '/dataManage',
        name: 'dataManage',
        component: dataManage,
        meta: {
          title: '数据管理后台',
          closeConfirm:'0'
        },
      },
      {
        path: '/addData',
        name: 'addData',
        component: addData,
        meta: {
          title: '数据管理',
          closeConfirm:'0'
        },
      },
      {
        path: '/editData',
        name: 'editData',
        component: editData,
        meta: {
          title: '数据管理',
          closeConfirm:'0'
        },
      },
      {
        path: '/viewData',
        name: 'viewData',
        component: viewData,
        meta: {
          title: '数据管理',
          closeConfirm:'0'
        },
      },
      // {
      //   path: '/bigscreen',
      //   name: 'bigscreen',
      //   component: bigscreen,
      //   meta: {
      //     title: '大屏',
      //     closeConfirm:'0'
      //   },
      // },
    ],
  }
]

const createRouter = () =>
  new Router({
    //mode: 'hash',
    mode: 'history',
    base: process.env.VUE_APP_PC_PATH + '/',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRouterMap,
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // 重置路由，不同用户菜单权限会有所不同，在切换用户时，会出现菜单错乱的情况
}
// 重新定义 router.go 方法
const originalGo = Router.prototype.go
Router.prototype.go = function (n, closeTag = true,reload=false) {
  if(Vue.config.layoutType === 'pc'){
    if (n === -1 && window.previousRoute.path === '/') {
      if (closeTag) {
        Event.$emit('DELTAB', true)
      }
      // 当前路由没有匹配项，执行返回首页的操作
      this.replace({ path: window.previousRoute.path })
    } else {
      if (closeTag) {
        Event.$emit('DELTAB', false, this.history.current,reload)
        originalGo.call(this, n)
      }
    }
  }else{
    originalGo.call(this, n)
  }
}
// 重新定义 router.push 方法
const originalPush = Router.prototype.push;

Router.prototype.push = function push(location) {
  const paramsKeys = Object.keys(location.params||{})
  if(paramsKeys.length>0){
    if(location.name){
      sessionStorage.setItem(location.name+'_session_params',JSON.stringify(location.params))
    }
    if(location.path){
      sessionStorage.setItem(location.name+'_session_params',JSON.stringify(location.params))
    }
  }
  // 调用原始的 push 方法
  return originalPush.call(this, location).catch(err => err);
};

export default router
