<template>
  <div>
    <!--    v-if="formPage == 'home'"-->
    <div class="p-5 pb-0">
      <!-- <el-breadcrumb separator="/" >
        <el-breadcrumb-item>
          <a @click="goBack">填报详情</a>
        </el-breadcrumb-item>
        <el-breadcrumb-item>去填报</el-breadcrumb-item>
      </el-breadcrumb> -->
    </div>
    <section class="app-container" style="margin-bottom: 10px">
      <VFormRender ref="VFormRender" :form-json="formJson" :form-data="formData" :preview-state="false"></VFormRender>
    </section>
    <section class="flex justify-center pb-8" >
      <!-- <el-button v-if="token" class="w-32" @click="goBack" :loading="loading">返回</el-button> -->
      <!-- <el-button v-if="token" class="w-32" @click="clearForm">重置数据</el-button> -->
      <el-button v-if="token" class="w-32" type="primary" @click="save(0)" :loading="loading">保存</el-button>
      <!-- <el-button v-if="token" v-show="!disable" class="w-32" type="primary" @click="save(0)" :loading="loading">保存</el-button> -->
      <!-- <el-button v-if="token" v-show="!disable" class="w-32" type="primary" :loading="loading" @click="save(1)" plain>继续填报</el-button> -->
      <el-button v-show="!disable" class="w-32" type="danger" @click="submitFn(1)">提交</el-button>
    </section>
  </div>
</template>

<script>
import { writeDataSubmitOut } from '@admin/api/fillData'
import VFormRender from '@/components/form-render/index'
import fillAction from "@/mixins/fillAction"
import fillEdit from "@/mixins/fillEdit"
import { getFromTaskDetailOut } from '@admin/api/formManager/own'
import { setFormDataToString,getFormDataParseString } from "@/components/form-render/utils"
import { getFromTaskDetail } from '@admin/api/formManager/own'
import { saveWriteData, writeDataDetail, autoShowFields,checkEdit } from '@admin/api/fillData'
import { getModelId } from '../../api/formManager/tpl'
// import { saveWriteData, writeDataDetail, autoShowFields,checkEdit } from '@admin/api/fillData'
import { getFormFillUrl } from '../../api/fillData'
import axios from 'axios';
import { parseParamKey } from '@/utils/parseToken'
export default {
  name: 'fillActionExternal',
  // mixins:[fillAction,fillEdit],
  components: {
    VFormRender,
  },
  data() {
    const { taskId, taskStaffId = '', writeDataId, disable=0,queryType=0,path=''} = this.$route.query
    return {
      disable:Number(disable)===1,
      pageLoading: false,
      formJson: {},
      formData: {},
      taskId,
      modelId:'',
      taskStaffId,
      loading: false,
      detail: {},
      writeData:{},
      handleFn:null,
      currChangeValue: '',
      ajaxFormData:{},
      upUrl:`/fillData/detail/index?taskId=${taskId}&taskStaffId=${taskStaffId}&queryType=${queryType||0}&pageName=home`,
      formPage:path,
      token: '',
      writeDataId:'',
    }
  },
  created(){
  },
  mounted() {
    console.log(123123);

    const search = window.location.search || window.location.hash.split('?')[1] || '';
    this.token = parseParamKey(search,'token')
    this.modelId = parseParamKey(search,'modelId')
    this.writeDataId = parseParamKey(search,'writeDataId')
    this.getFromTaskDetailOut()
  },
  methods: {
    goBack(){
      if(this.formPage === 'home'){
        this.$router.replace(this.upUrl)
      }else{
        this.$router.go(-1)
      }
    },
    clearForm(){
      this.$refs['VFormRender'].resetForm()
    },
    getFromTaskDetailOut() {
      this.pageLoading = true


      getFromTaskDetailOut(this.token?this.modelId:this.taskId)
        .then(res => {
          if (res.code === '00000') {
            this.detail = {
              ...(res?.data || {}),
            }
            this.formJson = JSON.parse(res?.data?.formJson ?? '{}')
            this.$refs['VFormRender']?.setFormJson(this.formJson)
            this.taskStaffId = res?.data?.formTaskStaffList[0].taskStaffId
            if (this.writeDataId) {
              this.$nextTick(()=>{
                this.writeDataDetail()
              })
            }
          }
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    writeDataDetail() {
      this.pageLoading = true
      const { widgetList } = this.formJson
      let subformKeys = []
      let arrList = []
      let numList = []
      let boolList = []
      widgetList.forEach(widget=>{
        if(widget.type === 'common'){
          console.log(widget);
          widget.widgetList.forEach(subWidget=>{
            if(subWidget.type === 'sub-form'){
              subformKeys.push(subWidget.options.name)
            }
          })
          widget.widgetList.forEach(subWidget=>{
            if(subWidget.type === 'checkbox' || subWidget.type === 'time-range' || subWidget.type === 'select' || subWidget.type === 'date-range'){
              arrList.push(subWidget.options.name)
            }
            if(subWidget.type === 'slider'){
              numList.push(subWidget.options.name)
            }
            if(subWidget.type === 'switch'){
              boolList.push(subWidget.options.name)
            }
          })
        }
      })
      console.log(arrList);
      writeDataDetail({
        taskStaffId: this.taskStaffId,
        writeDataId: this.writeDataId,
      }).then(res => {
        if (res.code === '00000'&&res.data.fieldsMap) {
          const dataTable = JSON.parse(this.detail.dataTable ?? '{}')
          const detial = this.writeData =  {
            ...(res.data ?? {})
          }
          const formObject = this.$refs['VFormRender'].getFormData(false)
          const subKeys = [] //子表单名称列表
          console.log(detial);
          Object.keys(formObject).forEach(key => {
            // 子表单
            if (subformKeys.includes(key)||key.indexOf('subform')>=0) {
              subKeys.push(key)
            } else {
              const { baseFields = [],otherFields =[]} = dataTable
              const typeObj = [...baseFields,...otherFields].find(item=>item.fieldKey == key)
              this.formData[key] =typeObj? getFormDataParseString(detial.fieldsMap[key],typeObj):detial.fieldsMap[key]
            }
          })
          subKeys.forEach((key,index) => {
            this.formData[key] = []
            const subTable = detial.subTables[index]
            subTable?.fieldsDatas?.forEach(item => {
              const obj = {}
              Object.keys(item || {}).forEach(subkey => {
                const typeObj =  dataTable.subTables[index]?.fields?.find(item=>item.fieldKey == subkey)
                obj[subkey] =typeObj? getFormDataParseString(item[subkey],typeObj):item[subkey]
              })
              this.formData[key].push(obj)
            })
          })
          this.formData = this.convertValueToArrayIfKeyExists(this.formData,arrList)
          this.formData = this.convertValueToNumberIfKeyExists(this.formData,numList)
          this.formData = this.convertValueToBooleanIfKeyExists(this.formData,boolList)
          this.$refs['VFormRender'].setFormData(this.formData)
          setTimeout(()=>{
            if(this.disable){
              this.$refs['VFormRender']?.disableForm()
            }
          },500)
        }
      }).finally(()=>{
        this.pageLoading = false
      })
    },
    convertValueToArrayIfKeyExists(obj, keysArray) {
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          // 如果 key 存在于给定的 keysArray 中
          if (keysArray.includes(key)) {
            const value = obj[key];

            // 如果 value 是字符串且包含逗号，则将其拆分成数组
            if (typeof value === 'string' && value.includes(',')) {
              obj[key] = value.split(',').map(item => item.trim()); // trim 去除多余空格
            } else if (!Array.isArray(value)) {
              // 如果 value 不是数组，则将其转为数组
              obj[key] = [value];
            }
          }
        }
      }
      return obj;
    },
    convertValueToNumberIfKeyExists(obj, keysArray) {
      for (let key in obj) {
        if (obj.hasOwnProperty(key) && keysArray.includes(key)) {
          // 将对应的值转换为数字类型
          obj[key] = Number(obj[key]);
        }
      }
      return obj;
    },
    convertValueToBooleanIfKeyExists(obj, keysArray) {
      for (let key in obj) {
        if (obj.hasOwnProperty(key) && keysArray.includes(key)) {
          // 将值转换为数字
          const numericValue = Number(obj[key]);

          // 将数字值转换为布尔类型：0 -> false, 非0的数字和NaN -> true
          obj[key] = !isNaN(numericValue) && numericValue !== 0;
        }
      }
      return obj;
    },
    submitFn2(){

    },
    submitFn(val){

        this.$refs['VFormRender'].getFormData().then((formData)=>{
          // if (!formData._departmentList) {
          //   this.$message({
          //     message: '请选择所属部门',
          //     type: 'warning',
          //   });
          //   return;
          // }

          //校验表单是否所有字段都是空
          const writeData =  Object.keys(formData).filter(key=>formData[key])
          //校验表单是否所有字段都是空
          if(writeData.length===0){
            this.$message({
              message: '表单数据为空，请核查数据',
              type: 'info',
            })
            return false
          }
          this.$confirm('确认提交数据?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.save(1,()=>{})

            if (this.token) {
              // 从url中获取taskId、userId、modelId、deptId数据
              const search = window.location.search || window.location.hash.split('?')[1] || '';
              const taskId = parseParamKey(search,'taskId');
              console.log("🚀 ~ submitFn ~ taskId:", taskId)
              const userId = parseParamKey(search,'userId');
              const type = parseParamKey(search,'type');
              getModelId(this.detail.taskName).then(res=>{
                if(res.code === '00000'){
                  if (taskId && userId && res.data.taskId && type) {
                    const para = {
                      taskId,
                      userId,
                      type,
                      modelId: res.data.taskId,
                      taskStaffId: this.taskStaffId,
                    };
                    getFormFillUrl(para);
                    try {
                      setTimeout(() => {
                        window.close()
                      }, 500)
                    } catch (error) {
                      setTimeout(() => {
                        // 跳转
                        window.location.replace("https://video.wgos.top/figure/template")
                      }, 500)
                    }

                  }
                }
              })
            }

            // this.submitFn1('数村政府')
          }).catch((err) => {
            console.log('取消',err)
          });
        })
    },
    submitFn1(){
      this.loading = true
      writeDataSubmitOut({
        taskStaffId: this.taskStaffId,
      }).then(res=>{
        if(res.code==='00000'){
          this.$message({
            message: res.msg,
            type: 'success',
          })
          //this.$router.go(-1,true,true)
          // this.$router.replace('/fillData/list/index')
        }
      }).finally(()=>{
        this.loading = false
      })
    },
    save(tag = 0,callBack) {
      const { widgetList } = this.formJson
      let subformKeys = []
      widgetList.forEach(widget=>{
        if(widget.type === 'common'){
          widget.widgetList.forEach(subWidget=>{
            if(subWidget.type === 'sub-form'){
              subformKeys.push(subWidget.options.name)
            }
          })
        }
      })
      this.$refs['VFormRender'].getFormData().then(formData => {
        //校验表单是否所有字段都是空
        const writeData =  Object.keys(formData).filter(key=>formData[key])

        const { fieldsMap ,subTables=[] }  =this.writeData
        const params = {
          taskStaffId: this.taskStaffId,
          fieldsMap: {
            ...fieldsMap
          },
          subTables: [],
          writeDataId:this.writeDataId??''
        }
        const dataTable = JSON.parse(this.detail.dataTable ?? '{}')
        //校验表单是否所有字段都是空
        if(writeData.length===0){
          if(this.formJson.formConfig.layoutType == 'H5'){
            this.$toast('表单数据为空，请核查数据')
          }else{
            this.$message({
              message: '表单数据为空，请核查数据',
              type: 'info',
            })
          }
          return false
        }
        this.loading = true
        Object.keys(formData).forEach(key => {
          const formItem = formData[key]
          // 子表单
          if (subformKeys.includes(key)||key.indexOf('subform')>=0) {
            const fieldsDatas = [],oldfieldsDatas = subTables[params.subTables.length]?.fieldsDatas||[]
            const len = params.subTables.length
            formItem?.forEach((item,index) => {
              if(oldfieldsDatas[index]?.id){
                item.id =oldfieldsDatas[index]?.id||null
                item.task_staff_id = oldfieldsDatas[index]?.task_staff_id||null
              }
              Object.keys(item).forEach(subkey=>{
                const typeObj =  dataTable.subTables[index]?.fields?.find(item=>item.fieldKey == subkey)
                item[subkey] = setFormDataToString(item[subkey],typeObj)
              })
              fieldsDatas.push(item)
            })
            params.subTables.push({
              table: dataTable?.subTables[len]?.table || '',
              fieldsDatas,
            })
          } else {
            const { baseFields = [],otherFields =[]} = dataTable
            const typeObj = [...baseFields,...otherFields].find(item=>item.fieldKey == key)
            params.fieldsMap[key] = setFormDataToString(formItem,typeObj)
          }
        })
        params.fieldsMap.data_status = '1'
      //   writeDataSubmitOut(params)
      //     .then(res => {
      //       if (res.code === '00000') {
      //         if (tag === 0) {
      //           if(this.formJson.formConfig.layoutType == 'H5'){
      //             this.$toast.success('成功')
      //           }else{
      //             this.$message({
      //               message: res.msg,
      //               type: 'success',
      //             })
      //           }
      //           this.$router.go(-1,true,true)
      //         } else if(tag === 1) {
      //           if(this.formJson.formConfig.layoutType == 'H5'){
      //             this.$toast.success('成功')
      //           }else{
      //             this.$message({
      //               message: res.msg,
      //               type: 'success',
      //             })
      //           }
      //           this.writeDataId = null
      //           this.$refs['VFormRender'].resetForm()
      //         }else if(tag === 2){
      //           //this.$router.go(-1,true,true)
      //           callBack&&callBack()
      //         }
      //       }
      //     })
      //     .finally(() => {
      //       this.loading = false
      //     })
      // })
        if(tag == 0 && this.token) {
          saveWriteData({...params})
            .then(res => {
              if (res.code === '00000') {
                if(this.formJson.formConfig.layoutType == 'H5'){
                  this.$toast.success('成功')
                }else{
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                }
                // if(this.formJson.formConfig.layoutType == 'H5'){
                //   this.$router.replace('/fill/own/detail')
                // }else{
                //   this.$router.replace('/fillData/list/index')
                // }
                setTimeout(() => {
                  // 跳转
                  window.location.replace("https://video.wgos.top/figure/template")
                }, 500)
              }
            })
            .finally(() => {
              this.loading = false
            })
          return
        }
        axios.post('/low-code-manage/formTaskStaff/out/submitData', params)
          .then(res => {
            if (res.code === '00000') {
              if (tag === 0) {
                if(this.formJson.formConfig.layoutType == 'H5'){
                  this.$toast.success('成功')
                }else{
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                }
                this.$router.go(-1,true,true)
              } else if(tag === 1) {
                if(this.formJson.formConfig.layoutType == 'H5'){
                  this.$toast.success('成功')
                }else{

                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  setTimeout(() => {
                    // 跳转
                    window.location.replace("https://video.wgos.top/figure/template")
                  }, 500)
                  return
                }
                this.writeDataId = null
                this.$refs['VFormRender'].resetForm()
              }else if(tag === 2){
                //this.$router.go(-1,true,true)
                callBack&&callBack()
              }
            }
          })
          .finally(() => {
            this.$toast.success('成功')
            this.loading = false
          })
      })
    },
  },
}
</script>

<style scoped></style>
