{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nvar _interopRequireWildcard = require(\"D:/Work/final\\u6570\\u79D1/cuc-cuc-qy-lowcode-lowcode-repository/lowcode-web/node_modules/@babel/runtime/helpers/interopRequireWildcard.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.loadMenus = void 0;\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.filter.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\nrequire(\"core-js/modules/web.url-search-params.delete.js\");\nrequire(\"core-js/modules/web.url-search-params.has.js\");\nrequire(\"core-js/modules/web.url-search-params.size.js\");\nvar _routers = _interopRequireWildcard(require(\"./routers\"));\nvar _store = _interopRequireDefault(require(\"@admin/store\"));\nvar _nprogress = _interopRequireDefault(require(\"nprogress\"));\nrequire(\"nprogress/nprogress.css\");\nvar _auth = require(\"@/utils/auth\");\nvar _menu = require(\"@admin/api/system/menu\");\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nvar _routeAndMenu = require(\"@/utils/routeAndMenu\");\nvar _fillData = require(\"../api/fillData\");\nvar _parseToken = require(\"@/utils/parseToken\");\n// progress bar\n// progress bar style\n// getToken from cookie\n\n_nprogress.default.configure({\n  showSpinner: false\n}); // NProgress Configuration\n\nconst whiteList = ['/login', '/403', '/fillActionExternal']; // no redirect whitelist\n\n// 修改或扩展路由参数\nfunction ovNext(to, from, next) {\n  const localData = sessionStorage.getItem(to.name + '_session_params') || sessionStorage.getItem(to.path + '_session_params');\n  if (localData) {\n    if (Object.keys(to.params || {}).length > 0) {\n      next();\n    } else {\n      // 构建一个新的路由对象，避免直接修改 to 对象\n      const newTo = {\n        ...to,\n        params: JSON.parse(localData),\n        repalce: to.path == from?.path?.replace('/redirect', '')\n      };\n      next(newTo);\n    }\n  } else {\n    next();\n  }\n}\nlet isTokenHandled = false; // 全局变量，标记是否已处理 token\n_routers.default.beforeEach((to, from, next) => {\n  if (isTokenHandled) {\n    next(); // 如果已经处理过 token，直接放行\n    return;\n  }\n  // console.log(to, from, next);\n  const search = window.location.search || window.location.hash.split('?')[1] || '';\n  const paramsUrl = new URLSearchParams(search);\n  debugger;\n  const token = paramsUrl.get('token') ? paramsUrl.get('token') : (0, _parseToken.parseParamKey)(search, 'token');\n  if (token) {\n    isTokenHandled = true; // 标记为已处理\n    (0, _fillData.queryUrlToken)({\n      token,\n      mode: 2\n    }).then(res => {\n      console.log(\"🚀 ~ res:\", res);\n      (0, _auth.setToken)(res.data.accessToken);\n      sessionStorage.setItem('user_info', JSON.stringify(res.data.userInfo));\n      const url = window.location.pathname.replace('/lowcode-pc', '') + window.location.search;\n      next(url); // 否则全部重定向到登录页\n      // debugger\n    });\n    return;\n  }\n  window.previousRoute = from;\n  if (to.meta.title) {\n    document.title = _jsCookie.default.get('systemName') ? _jsCookie.default.get('systemName') + ' - ' + to.meta.title : '开发框架' + ' - ' + to.meta.title;\n  }\n  _nprogress.default.start();\n  // //链接 /lowcode-pc/?authCode=xxxx\n  if (to.query.authCode && to.path !== '/login') {\n    const path = '/';\n    next(`/login?redirect=${path}&authCode=${to.query.authCode}`); // 重定向到登录页\n    _nprogress.default.done(); // finish progress bar\n  } else {\n    console.log(to.path);\n    if (whiteList.indexOf(to.path) !== -1) {\n      // 在免登录白名单，直接进入\n      ovNext(to, from, next);\n    } else if ((0, _auth.getToken)()) {\n      // 已登录且要跳转的页面是登录页\n      if (to.path === '/login') {\n        next({\n          path: '/'\n        });\n        _nprogress.default.done();\n      } else {\n        if (_store.default.getters.roles.length === 0) {\n          // 判断当前用户是否已拉取完user_info信息\n          _store.default.dispatch('GetInfo').then(() => {\n            loadMenus(next, to);\n          });\n        } else if (!_store.default.getters.permission_routesLoaded) {\n          loadMenus(next, to);\n        } else {\n          ovNext(to, from, next);\n        }\n      }\n    } else {\n      /* has no token*/\n      // 在模块中调用重置路由\n      (0, _routers.resetRouter)();\n      // 清除tab标签缓存\n      _store.default.dispatch('tagsView/delAllViews');\n      const path = '/';\n      next(`/login?redirect=${path}`); // 否则全部重定向到登录页\n      _nprogress.default.done();\n    }\n  }\n});\nconst loadMenus = (next, to) => {\n  let buildMenus = [];\n  (0, _menu.buildMenu)().then(res => {\n    buildMenus = res.data;\n    (0, _menu.published)().then(ress => {\n      if (ress.code == '00000') {\n        buildMenus.map(item => {\n          if (item.path == '/information') {\n            item.children.map(i => {\n              if (i.path == 'putOn') {\n                ress.data.list.map(item => {\n                  i.children.push({\n                    \"name\": \"putOnDataListing\" + item.dataId,\n                    \"path\": \"/putOn/dataListing?dataId=\" + item.dataId + '&taskId=' + item.taskId,\n                    \"hidden\": false,\n                    \"redirect\": null,\n                    \"component\": \"putOn/dataListing\",\n                    \"alwaysShow\": null,\n                    \"meta\": {\n                      \"title\": item.dataName,\n                      \"taskId\": item.dataId,\n                      \"icon\": \"m-menu\",\n                      \"noCache\": true,\n                      \"iFrame\": \"1\",\n                      \"closeConfirm\": \"0\",\n                      \"selectedFields\": item.fields\n                    },\n                    \"children\": null\n                  });\n                });\n              }\n            });\n          }\n        });\n        (0, _routeAndMenu.generateRroutes)(buildMenus, next, to);\n      }\n    });\n    (0, _menu.pageMyApply)({\n      \"auditStatus\": \"0\",\n      \"dataId\": \"\",\n      \"dataName\": \"\",\n      \"applyTime\": \"\",\n      \"auditTime\": \"\",\n      \"applyStatus\": \"3\",\n      \"pageNum\": 1,\n      \"pageSize\": 10,\n      \"orderType\": \"\",\n      \"orderBy\": \"\",\n      \"applicationId\": \"1660471189374717953\"\n    }).then(ress => {\n      if (ress.code == '00000') {\n        buildMenus.map(item => {\n          if (item.path == '/information') {\n            item.children.map(i => {\n              if (i.path == 'myDataApply') {\n                const uniqueArr = ress.data.list.filter((value, index, self) => index === self.findIndex(t => t.dataId === value.dataId));\n                uniqueArr.map(item => {\n                  i.children.push({\n                    \"name\": \"formDataApply\" + item.dataId,\n                    \"path\": \"/myDataApply/dataListing?dataId=\" + item.dataId + '&taskId=' + item.taskId + '&applyId=' + item.applyId,\n                    \"hidden\": false,\n                    \"redirect\": null,\n                    \"component\": \"myDataApply/dataListing\",\n                    \"alwaysShow\": null,\n                    \"meta\": {\n                      \"title\": item.dataName,\n                      \"taskId\": item.dataId,\n                      \"icon\": \"m-menu\",\n                      \"noCache\": true,\n                      \"iFrame\": \"1\",\n                      \"closeConfirm\": \"0\",\n                      \"selectedFields\": item.selectedFields\n                    },\n                    \"children\": null\n                  });\n                });\n              }\n            });\n          }\n        });\n        (0, _routeAndMenu.generateRroutes)(buildMenus, next, to);\n      }\n    });\n  });\n};\nexports.loadMenus = loadMenus;\n_routers.default.afterEach(() => {\n  _nprogress.default.done(); // finish progress bar\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}